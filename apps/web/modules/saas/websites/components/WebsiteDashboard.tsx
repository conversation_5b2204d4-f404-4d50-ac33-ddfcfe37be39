"use client";

import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { StatsTile } from "@saas/start/components/StatsTile";
import { But<PERSON> } from "@ui/components/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { 
	ExternalLinkIcon, 
	SettingsIcon, 
	BarChart3Icon, 
	PaletteIcon,
	GlobeIcon,
	LoaderIcon,
	AlertCircleIcon
} from "lucide-react";
import { useEffect, useState } from "react";
import { GeneratedWebsite } from "../utils/website-generator";
import { WebsiteService } from "../lib/website-service";
import { WebsiteSettingsModal } from "./WebsiteSettingsModal";

export function WebsiteDashboard() {
	const { activeOrganization } = useActiveOrganization();
	const [website, setWebsite] = useState<GeneratedWebsite | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [settingsOpen, setSettingsOpen] = useState(false);

	useEffect(() => {
		if (activeOrganization) {
			loadWebsite();
		}
	}, [activeOrganization]);

	const loadWebsite = async () => {
		if (!activeOrganization) return;
		
		try {
			setLoading(true);
			const websiteData = await WebsiteService.getWebsiteByOrganization(activeOrganization.id);
			setWebsite(websiteData);
			setError(null);
		} catch (err) {
			setError('Failed to load website data');
			console.error('Error loading website:', err);
		} finally {
			setLoading(false);
		}
	};

	const refreshAnalytics = async () => {
		if (!website) return;
		
		try {
			const analytics = await WebsiteService.getWebsiteAnalytics(website.id);
			if (analytics) {
				setWebsite(prev => prev ? { ...prev, analytics } : null);
			}
		} catch (err) {
			console.error('Error refreshing analytics:', err);
		}
	};

	if (loading) {
		return (
			<div className="flex items-center justify-center py-12">
				<div className="flex items-center gap-3">
					<LoaderIcon className="size-5 animate-spin" />
					<span className="text-muted-foreground">Loading website data...</span>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="flex items-center justify-center py-12">
				<div className="flex items-center gap-3 text-destructive">
					<AlertCircleIcon className="size-5" />
					<span>{error}</span>
				</div>
			</div>
		);
	}

	if (!website) {
		return (
			<div className="text-center py-12">
				<div className="max-w-md mx-auto">
					<GlobeIcon className="size-12 mx-auto text-muted-foreground mb-4" />
					<h3 className="text-lg font-semibold mb-2">No Website Found</h3>
					<p className="text-muted-foreground mb-4">
						It looks like your website hasn't been generated yet. This usually happens automatically during onboarding.
					</p>
					<Button onClick={loadWebsite}>
						Refresh
					</Button>
				</div>
			</div>
		);
	}

	const { analytics, config, status, url } = website;
	const templateInfo = WebsiteService.getTemplateInfo(config.template);

	return (
		<div className="space-y-6">
			{/* Website Status Card */}
			<Card>
				<CardHeader>
					<div className="flex items-center justify-between">
						<div>
							<CardTitle className="flex items-center gap-2">
								<GlobeIcon className="size-5" />
								Your Website
							</CardTitle>
							<CardDescription>
								{templateInfo.name} template • {templateInfo.description}
							</CardDescription>
						</div>
						<Badge 
							variant={status === 'active' ? 'default' : status === 'generating' ? 'secondary' : 'destructive'}
							className="capitalize"
						>
							{status === 'generating' && <LoaderIcon className="size-3 mr-1 animate-spin" />}
							{status}
						</Badge>
					</div>
				</CardHeader>
				<CardContent>
					<div className="flex items-center justify-between">
						<div className="space-y-1">
							<p className="font-medium">{url}</p>
							<p className="text-sm text-muted-foreground">
								Last updated: {new Date(website.updatedAt).toLocaleDateString()}
							</p>
						</div>
						<div className="flex gap-2">
							<Button
								variant="outline"
								size="sm"
								onClick={() => setSettingsOpen(true)}
							>
								<SettingsIcon className="size-4 mr-2" />
								Settings
							</Button>
							<Button size="sm" asChild>
								<a href={url} target="_blank" rel="noopener noreferrer">
									<ExternalLinkIcon className="size-4 mr-2" />
									View Site
								</a>
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Analytics Stats */}
			<div className="@container">
				<div className="flex items-center justify-between mb-4">
					<h2 className="text-lg font-semibold flex items-center gap-2">
						<BarChart3Icon className="size-5" />
						Website Analytics
					</h2>
					<Button variant="outline" size="sm" onClick={refreshAnalytics}>
						Refresh Data
					</Button>
				</div>
				
				<div className="grid @2xl:grid-cols-4 @lg:grid-cols-2 gap-4">
					<StatsTile
						title="Total Visitors"
						value={analytics.totalVisitors}
						valueFormat="number"
						trend={0.15}
						subtitle="All time"
					/>
					<StatsTile
						title="Monthly Visitors"
						value={analytics.monthlyVisitors}
						valueFormat="number"
						trend={0.08}
						subtitle="This month"
					/>
					<StatsTile
						title="Page Views"
						value={analytics.pageViews}
						valueFormat="number"
						trend={0.12}
						subtitle="Total views"
					/>
					<StatsTile
						title="Bounce Rate"
						value={analytics.bounceRate / 100}
						valueFormat="percentage"
						trend={-0.05}
						subtitle="Lower is better"
					/>
				</div>
			</div>

			{/* Conversion Stats */}
			<Card>
				<CardHeader>
					<CardTitle>Customer Actions</CardTitle>
					<CardDescription>
						How visitors are engaging with your business
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="grid @lg:grid-cols-2 @2xl:grid-cols-4 gap-4">
						<div className="text-center p-4 bg-muted/50 rounded-lg">
							<div className="text-2xl font-bold text-blue-600">
								{analytics.conversions.phoneClicks}
							</div>
							<div className="text-sm text-muted-foreground">Phone Clicks</div>
						</div>
						<div className="text-center p-4 bg-muted/50 rounded-lg">
							<div className="text-2xl font-bold text-green-600">
								{analytics.conversions.directionsClicks}
							</div>
							<div className="text-sm text-muted-foreground">Directions Clicked</div>
						</div>
						<div className="text-center p-4 bg-muted/50 rounded-lg">
							<div className="text-2xl font-bold text-purple-600">
								{analytics.conversions.contactForms}
							</div>
							<div className="text-sm text-muted-foreground">Contact Forms</div>
						</div>
						<div className="text-center p-4 bg-muted/50 rounded-lg">
							<div className="text-2xl font-bold text-orange-600">
								{analytics.conversions.websiteClicks}
							</div>
							<div className="text-sm text-muted-foreground">Website Visits</div>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Traffic Sources */}
			<div className="grid @lg:grid-cols-2 gap-6">
				<Card>
					<CardHeader>
						<CardTitle>Top Pages</CardTitle>
						<CardDescription>Most visited pages on your website</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-3">
							{analytics.topPages.map((page, index) => (
								<div key={page.path} className="flex items-center justify-between">
									<div>
										<div className="font-medium">{page.title}</div>
										<div className="text-sm text-muted-foreground">{page.path}</div>
									</div>
									<div className="text-right">
										<div className="font-medium">{page.views}</div>
										<div className="text-sm text-muted-foreground">views</div>
									</div>
								</div>
							))}
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Traffic Sources</CardTitle>
						<CardDescription>Where your visitors are coming from</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-3">
							{analytics.trafficSources.map((source, index) => (
								<div key={source.source} className="flex items-center justify-between">
									<div className="font-medium">{source.source}</div>
									<div className="text-right">
										<div className="font-medium">{source.visitors}</div>
										<div className="text-sm text-muted-foreground">{source.percentage}%</div>
									</div>
								</div>
							))}
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Settings Modal */}
			{website && (
				<WebsiteSettingsModal
					website={website}
					open={settingsOpen}
					onOpenChange={setSettingsOpen}
					onWebsiteUpdate={setWebsite}
				/>
			)}
		</div>
	);
}
