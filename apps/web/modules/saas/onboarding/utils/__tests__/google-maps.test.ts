import { describe, it, expect } from '@jest/globals';
import { extractPlacesIdFromUrl, isGoogleMapsUrl } from '../google-maps';

describe('Google Maps Utilities', () => {
	describe('isGoogleMapsUrl', () => {
		it('should return true for valid Google Maps URLs', () => {
			const validUrls = [
				'https://maps.google.com/maps?cid=123456789',
				'https://www.google.com/maps/place/Business+Name/@40.7128,-74.0060,15z',
				'https://goo.gl/maps/abc123',
				'https://maps.app.goo.gl/abc123',
			];

			validUrls.forEach(url => {
				expect(isGoogleMapsUrl(url)).toBe(true);
			});
		});

		it('should return false for invalid URLs', () => {
			const invalidUrls = [
				'https://example.com',
				'https://facebook.com',
				'not-a-url',
				'',
			];

			invalidUrls.forEach(url => {
				expect(isGoogleMapsUrl(url)).toBe(false);
			});
		});
	});

	describe('extractPlacesIdFromUrl', () => {
		it('should extract Places ID from CID format', () => {
			const url = 'https://maps.google.com/maps?cid=1234567890123456789';
			const result = extractPlacesIdFromUrl(url);
			expect(result).toBe('1234567890123456789');
		});

		it('should extract Places ID from place format', () => {
			const url = 'https://www.google.com/maps/place/Business+Name/@40.7128,-74.0060,15z/data=!3m1!4b1!4m6!3m5!1s0x89c25a316e5b7c5d:0x1234567890abcdef!8m2!3d40.7128!4d-74.0060!16s%2Fg%2F11abc123def';
			const result = extractPlacesIdFromUrl(url);
			expect(result).toBe('0x89c25a316e5b7c5d:0x1234567890abcdef');
		});

		it('should return null for URLs without Places ID', () => {
			const invalidUrls = [
				'https://maps.google.com',
				'https://www.google.com/maps',
				'https://example.com',
				'',
			];

			invalidUrls.forEach(url => {
				expect(extractPlacesIdFromUrl(url)).toBeNull();
			});
		});

		it('should handle malformed URLs gracefully', () => {
			const malformedUrls = [
				'not-a-url',
				'https://',
				null,
				undefined,
			];

			malformedUrls.forEach(url => {
				expect(extractPlacesIdFromUrl(url as string)).toBeNull();
			});
		});
	});
});
